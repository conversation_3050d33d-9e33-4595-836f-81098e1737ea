/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.admincommandhandlers;

import java.util.List;
import java.util.StringTokenizer;

import org.l2jmobius.gameserver.handler.IAdminCommandHandler;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * Admin command handler for testing costume system
 * <AUTHOR>
 */
public class AdminCostumeTest implements IAdminCommandHandler
{
	private static final String[] ADMIN_COMMANDS =
	{
		"admin_costume_test",
		"admin_costume_shortcuts",
		"admin_costume_cleanup",
		"admin_costume_validate",
		"admin_costume_refresh",
		"admin_costume_evolution",
		"admin_costume_extraction",
		"admin_costume_debug",
		"admin_costume_reload",
		"admin_costume_give",
		"admin_costume_login",
		"admin_costume_remove_shortcuts"
	};
	
	@Override
	public boolean useAdminCommand(String command, Player activeChar)
	{
		final StringTokenizer st = new StringTokenizer(command, " ");
		final String actualCommand = st.nextToken();
		
		switch (actualCommand.toLowerCase())
		{
			case "admin_costume_test":
			{
				handleCostumeTest(activeChar);
				break;
			}
			case "admin_costume_shortcuts":
			{
				handleShortcutTest(activeChar);
				break;
			}
			case "admin_costume_cleanup":
			{
				handleCleanup(activeChar);
				break;
			}
			case "admin_costume_validate":
			{
				handleValidation(activeChar);
				break;
			}
			case "admin_costume_refresh":
			{
				handleRefresh(activeChar);
				break;
			}
			case "admin_costume_evolution":
			{
				handleEvolutionTest(activeChar);
				break;
			}
			case "admin_costume_extraction":
			{
				handleExtractionTest(activeChar);
				break;
			}
			case "admin_costume_debug":
			{
				handleDebugTest(activeChar);
				break;
			}
			case "admin_costume_reload":
			{
				handleReloadCostumes(activeChar);
				break;
			}
			case "admin_costume_give":
			{
				handleGiveCostume(activeChar, st);
				break;
			}
			case "admin_costume_login":
			{
				handleLoginEvent(activeChar);
				break;
			}
			case "admin_costume_remove_shortcuts":
			{
				handleRemoveShortcuts(activeChar, st);
				break;
			}
		}
		
		return true;
	}
	
	private void handleCostumeTest(Player activeChar)
	{
		activeChar.sendMessage("=== Running Full Costume System Test ===");
		CostumeSystemTest.runFullSystemTest(activeChar);
		activeChar.sendMessage("=== Costume System Test Completed ===");
	}
	
	private void handleShortcutTest(Player activeChar)
	{
		activeChar.sendMessage("=== Testing Costume Shortcuts ===");
		
		// Load and display current shortcuts
		List<CostumeShortcutInfo> shortcuts = CostumeShortcutManager.loadPlayerShortcuts(activeChar.getObjectId());
		activeChar.sendMessage("Current shortcuts: " + shortcuts.size());
		
		for (CostumeShortcutInfo shortcut : shortcuts)
		{
			activeChar.sendMessage("- Page: " + shortcut.getPage() + ", Slot: " + shortcut.getSlot() + ", Costume: " + shortcut.getCostumeId());
		}
		
		// Test position validation
		activeChar.sendMessage("Position validation tests:");
		activeChar.sendMessage("- (0,0): " + CostumeShortcutManager.isValidPosition(0, 0));
		activeChar.sendMessage("- (9,11): " + CostumeShortcutManager.isValidPosition(9, 11));
		activeChar.sendMessage("- (-1,0): " + CostumeShortcutManager.isValidPosition(-1, 0));
		activeChar.sendMessage("- (10,12): " + CostumeShortcutManager.isValidPosition(10, 12));
		
		activeChar.sendMessage("=== Shortcut Test Completed ===");
	}
	
	private void handleCleanup(Player activeChar)
	{
		activeChar.sendMessage("=== Cleaning Up Invalid Shortcuts ===");
		
		int cleaned = CostumeShortcutManager.cleanupInvalidShortcuts(activeChar.getObjectId());
		activeChar.sendMessage("Cleaned up " + cleaned + " invalid shortcuts");
		
		// Refresh costume list
		activeChar.sendPacket(new ExSendCostumeListFull());
		activeChar.sendMessage("Costume list refreshed");
		
		activeChar.sendMessage("=== Cleanup Completed ===");
	}
	
	private void handleValidation(Player activeChar)
	{
		activeChar.sendMessage("=== Validating Costume System ===");
		
		CostumeManager manager = CostumeManager.getInstance();
		
		// Test costume ownership
		List<Costume> costumes = manager.getCostumesForPlayer(activeChar);
		activeChar.sendMessage("Player owns " + costumes.size() + " costumes:");
		
		for (Costume costume : costumes)
		{
			boolean canCreate = CostumeShortcutManager.canCreateShortcut(activeChar, costume.getId());
			activeChar.sendMessage("- Costume " + costume.getId() + " (Amount: " + costume.getAmount() + ", Can create shortcut: " + canCreate + ")");
		}
		
		// Test position validation
		activeChar.sendMessage("Position validation:");
		activeChar.sendMessage("- Valid position (5,5): " + manager.isValidShortcutPosition(5, 5));
		activeChar.sendMessage("- Invalid position (15,15): " + manager.isValidShortcutPosition(15, 15));
		
		activeChar.sendMessage("=== Validation Completed ===");
	}
	
	private void handleRefresh(Player activeChar)
	{
		activeChar.sendMessage("=== Refreshing Costume Data ===");

		// Send updated costume list
		activeChar.sendPacket(new ExSendCostumeListFull());

		activeChar.sendMessage("Costume data refreshed successfully");
	}

	private void handleEvolutionTest(Player activeChar)
	{
		activeChar.sendMessage("=== Testing Costume Evolution ===");

		// Test evolution capabilities
		List<Costume> costumes = CostumeManager.getInstance().getCostumesForPlayer(activeChar);
		activeChar.sendMessage("Testing evolution for " + costumes.size() + " costumes:");

		for (Costume costume : costumes)
		{
			int costumeId = costume.getId();
			boolean canEvolve = CostumeEvolutionManager.canEvolve(activeChar, costumeId);
			int evolutionFee = CostumeEvolutionManager.getEvolutionFee(costumeId);
			boolean hasNext = CostumeEvolutionManager.hasNextEvolution(costumeId);

			activeChar.sendMessage("- Costume " + costumeId + ": Can evolve=" + canEvolve +
									", Fee=" + evolutionFee + ", Has next=" + hasNext);
		}

		activeChar.sendMessage("=== Evolution Test Completed ===");
	}

	private void handleExtractionTest(Player activeChar)
	{
		activeChar.sendMessage("=== Testing Costume Extraction ===");

		// Test extraction capabilities
		List<Costume> costumes = CostumeManager.getInstance().getCostumesForPlayer(activeChar);
		activeChar.sendMessage("Testing extraction for " + costumes.size() + " costumes:");

		for (Costume costume : costumes)
		{
			int costumeId = costume.getId();
			boolean canExtract = CostumeExtractionManager.canExtract(activeChar, costumeId, 1);
			int extractItem = CostumeExtractionManager.getExtractionItem(costumeId);
			boolean isExtractable = CostumeExtractionManager.isExtractable(costumeId);

			activeChar.sendMessage("- Costume " + costumeId + ": Can extract=" + canExtract +
									", Extract item=" + extractItem + ", Is extractable=" + isExtractable);
		}

		activeChar.sendMessage("=== Extraction Test Completed ===");
	}

	private void handleDebugTest(Player activeChar)
	{
		activeChar.sendMessage("=== Debug Costume System ===");

		// Debug costume manager
		CostumeManager manager = CostumeManager.getInstance();
		activeChar.sendMessage("CostumeManager loaded costumes: " + manager.getCostumes().size());

		// Debug specific costumes
		for (int costumeId = 1; costumeId <= 5; costumeId++)
		{
			CostumeRecord record = manager.getCostume(costumeId);
			if (record != null)
			{
				activeChar.sendMessage("Costume " + costumeId + ": ExtractItem=" + record.extractItem() +
										", EvolutionFee=" + record.evolutionFee() + ", Skill=" + record.skill().getId());
			}
			else
			{
				activeChar.sendMessage("Costume " + costumeId + ": NOT FOUND in system");
			}

			// Check player's costume
			Costume playerCostume = activeChar.getCostume(costumeId);
			if (playerCostume != null)
			{
				activeChar.sendMessage("Player has costume " + costumeId + ": Amount=" + playerCostume.getAmount() +
										", Locked=" + playerCostume.isLocked());
			}
			else
			{
				activeChar.sendMessage("Player does NOT have costume " + costumeId);
			}
		}

		// Test extraction validation
		activeChar.sendMessage("Testing extraction validation:");
		for (int costumeId = 1; costumeId <= 2; costumeId++)
		{
			boolean canExtract = CostumeExtractionManager.canExtract(activeChar, costumeId, 1);
			activeChar.sendMessage("Can extract costume " + costumeId + ": " + canExtract);
		}

		activeChar.sendMessage("=== Debug Test Completed ===");
	}

	private void handleReloadCostumes(Player activeChar)
	{
		activeChar.sendMessage("=== Reloading Player Costumes ===");

		// Force reload costumes
		CostumeManager.getInstance().processCollections(activeChar);

		// Check results
		activeChar.sendMessage("Player now has " + activeChar.getCostumeAmount() + " costumes");

		// Test specific costumes
		for (int costumeId = 1; costumeId <= 2; costumeId++)
		{
			Costume costume = activeChar.getCostume(costumeId);
			if (costume != null)
			{
				activeChar.sendMessage("Costume " + costumeId + ": Amount=" + costume.getAmount() + ", Locked=" + costume.isLocked());
			}
			else
			{
				activeChar.sendMessage("Costume " + costumeId + ": NOT FOUND after reload");
			}
		}

		activeChar.sendMessage("=== Reload Completed ===");
	}

	private void handleGiveCostume(Player activeChar, StringTokenizer st)
	{
		if (!st.hasMoreTokens())
		{
			activeChar.sendMessage("Usage: //admin_costume_give <costumeId> [amount]");
			return;
		}

		try
		{
			int costumeId = Integer.parseInt(st.nextToken());
			int amount = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;

			// Check if costume exists in system
			CostumeRecord record = CostumeManager.getInstance().getCostume(costumeId);
			if (record == null)
			{
				activeChar.sendMessage("Costume " + costumeId + " does not exist in system");
				return;
			}

			// Give costume to player
			Costume costume = activeChar.addCostume(costumeId);
			for (int i = 1; i < amount; i++)
			{
				costume.increaseAmount();
			}

			activeChar.sendMessage("Given costume " + costumeId + " x" + amount + " to player");
			activeChar.sendPacket(new ExSendCostumeListFull());
		}
		catch (NumberFormatException e)
		{
			activeChar.sendMessage("Invalid costume ID or amount");
		}
	}

	private void handleLoginEvent(Player activeChar)
	{
		activeChar.sendMessage("=== Triggering Costume Login Event ===");

		try
		{
			// Manually trigger the login event
			CostumeManager.getInstance().processCollections(activeChar);
			activeChar.sendPacket(new ExSendCostumeListFull());

			activeChar.sendMessage("Login event triggered successfully");
			activeChar.sendMessage("Player now has " + activeChar.getCostumeAmount() + " costumes");

			// Test specific costumes
			for (int costumeId = 1; costumeId <= 2; costumeId++)
			{
				Costume costume = activeChar.getCostume(costumeId);
				if (costume != null)
				{
					activeChar.sendMessage("Costume " + costumeId + ": Amount=" + costume.getAmount());
				}
				else
				{
					activeChar.sendMessage("Costume " + costumeId + ": NOT FOUND");
				}
			}
		}
		catch (Exception e)
		{
			activeChar.sendMessage("Error triggering login event: " + e.getMessage());
			e.printStackTrace();
		}

		activeChar.sendMessage("=== Login Event Completed ===");
	}

	private void handleRemoveShortcuts(Player activeChar, StringTokenizer st)
	{
		if (!st.hasMoreTokens())
		{
			activeChar.sendMessage("Usage: //admin_costume_remove_shortcuts <costumeId>");
			return;
		}

		try
		{
			int costumeId = Integer.parseInt(st.nextToken());

			activeChar.sendMessage("=== Removing Shortcuts for Costume " + costumeId + " ===");

			// Remove shortcuts
			boolean removed = CostumeShortcutManager.removeShortcutsByCostume(activeChar.getObjectId(), costumeId);

			if (removed)
			{
				activeChar.sendMessage("Shortcuts removed successfully");

				// Refresh client
				activeChar.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(activeChar.getObjectId()));
				activeChar.sendPacket(new ExSendCostumeListFull());

				activeChar.sendMessage("Client refreshed");
			}
			else
			{
				activeChar.sendMessage("Failed to remove shortcuts");
			}
		}
		catch (NumberFormatException e)
		{
			activeChar.sendMessage("Invalid costume ID");
		}
		catch (Exception e)
		{
			activeChar.sendMessage("Error: " + e.getMessage());
		}
	}

	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
}
