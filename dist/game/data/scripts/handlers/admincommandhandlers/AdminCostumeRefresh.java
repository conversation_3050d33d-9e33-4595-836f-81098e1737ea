/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.admincommandhandlers;

import java.util.StringTokenizer;

import org.l2jmobius.gameserver.handler.IAdminCommandHandler;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * Admin command to refresh costume system
 * <AUTHOR>
 */
public class AdminCostumeRefresh implements IAdminCommandHandler
{
	private static final String[] ADMIN_COMMANDS =
	{
		"admin_costume_refresh",
		"admin_costume_force_refresh",
		"admin_costume_reload",
		"admin_costume_clear_shortcuts",
		"admin_costume_test_lock"
	};
	
	@Override
	public boolean useAdminCommand(String command, Player activeChar)
	{
		final StringTokenizer st = new StringTokenizer(command, " ");
		final String actualCommand = st.nextToken();
		
		switch (actualCommand.toLowerCase())
		{
			case "admin_costume_refresh":
			{
				handleRefresh(activeChar);
				break;
			}
			case "admin_costume_force_refresh":
			{
				handleForceRefresh(activeChar);
				break;
			}
			case "admin_costume_reload":
			{
				handleReload(activeChar);
				break;
			}
			case "admin_costume_clear_shortcuts":
			{
				handleClearShortcuts(activeChar);
				break;
			}
			case "admin_costume_test_lock":
			{
				handleTestLock(activeChar);
				break;
			}
		}
		
		return true;
	}
	
	private void handleRefresh(Player activeChar)
	{
		activeChar.sendMessage("=== Costume Refresh ===");
		
		try
		{
			// Refresh costume collections
			CostumeManager.getInstance().processCollections(activeChar);
			activeChar.sendMessage("✓ Costume collections refreshed");
			
			// Send updated costume list
			activeChar.sendPacket(new ExSendCostumeListFull());
			activeChar.sendMessage("✓ Costume list packet sent");
			
			// Refresh inventory
			activeChar.sendItemList();
			activeChar.sendMessage("✓ Inventory refreshed");
			
			activeChar.sendMessage("=== Refresh Completed ===");
		}
		catch (Exception e)
		{
			activeChar.sendMessage("❌ Error during refresh: " + e.getMessage());
		}
	}
	
	private void handleForceRefresh(Player activeChar)
	{
		activeChar.sendMessage("=== Force Costume Refresh ===");
		
		try
		{
			// Force reload from database
			CostumeManager.getInstance().processCollections(activeChar);
			activeChar.sendMessage("✓ Collections reprocessed");
			
			// Send multiple refresh packets with delays
			activeChar.sendPacket(new ExSendCostumeListFull());
			activeChar.sendMessage("✓ First costume list sent");
			
			// Schedule delayed refresh
			org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
				activeChar.sendPacket(new ExSendCostumeListFull());
				activeChar.sendMessage("✓ Second costume list sent");
				
				// Final refresh
				org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
					activeChar.sendItemList();
					activeChar.sendMessage("✓ Final inventory refresh");
					activeChar.sendMessage("=== Force Refresh Completed ===");
				}, 200);
			}, 100);
		}
		catch (Exception e)
		{
			activeChar.sendMessage("❌ Error during force refresh: " + e.getMessage());
		}
	}
	
	private void handleReload(Player activeChar)
	{
		activeChar.sendMessage("=== Costume System Reload ===");
		
		try
		{
			// Reload costume manager
			CostumeManager.getInstance().load();
			activeChar.sendMessage("✓ Costume manager reloaded");
			
			// Refresh player data
			CostumeManager.getInstance().processCollections(activeChar);
			activeChar.sendMessage("✓ Player costume data refreshed");
			
			// Send updated packets
			activeChar.sendPacket(new ExSendCostumeListFull());
			activeChar.sendMessage("✓ Costume list updated");
			
			activeChar.sendMessage("=== Reload Completed ===");
		}
		catch (Exception e)
		{
			activeChar.sendMessage("❌ Error during reload: " + e.getMessage());
		}
	}

	private void handleClearShortcuts(Player activeChar)
	{
		activeChar.sendMessage("=== Clear Costume Shortcuts ===");

		try
		{
			// Send shortcut list refresh
			activeChar.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(activeChar.getObjectId()));
			activeChar.sendMessage("✓ Sent shortcut list refresh");

			// Schedule delayed refresh with costume list
			org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
				activeChar.sendPacket(new ExSendCostumeListFull());
				activeChar.sendMessage("✓ Sent costume list refresh");

				// Send another shortcut refresh
				activeChar.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(activeChar.getObjectId()));
				activeChar.sendMessage("✓ Sent final shortcut list refresh");

				activeChar.sendMessage("=== Clear Shortcuts Completed ===");
			}, 500);
		}
		catch (Exception e)
		{
			activeChar.sendMessage("❌ Error clearing shortcuts: " + e.getMessage());
		}
	}

	private void handleTestLock(Player activeChar)
	{
		activeChar.sendMessage("=== Test Costume Lock ===");

		try
		{
			// Get player's costumes using static method
			java.util.List<org.l2jmobius.gameserver.model.costumes.Costume> costumes =
				org.l2jmobius.gameserver.model.costumes.Costume.getCostumesForPlayer(activeChar);

			if (costumes.isEmpty())
			{
				activeChar.sendMessage("❌ No costumes found to test lock");
				return;
			}

			// Test lock/unlock first costume
			org.l2jmobius.gameserver.model.costumes.Costume firstCostume = costumes.get(0);
			boolean currentLock = firstCostume.isLocked();
			boolean newLock = !currentLock;

			activeChar.sendMessage("Testing costume " + firstCostume.getId() + " - Current lock: " + currentLock + " -> New lock: " + newLock);

			// Change lock status
			firstCostume.setLockedAndUpdate(newLock);

			// Send packets
			activeChar.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeLock(firstCostume.getId(), newLock, true));
			activeChar.sendMessage("✓ Sent ExCostumeLock packet");

			// Schedule delayed refresh
			org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
				activeChar.sendPacket(new ExSendCostumeListFull());
				activeChar.sendMessage("✓ Sent costume list refresh");

				activeChar.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(activeChar.getObjectId()));
				activeChar.sendMessage("✓ Sent shortcut list refresh");

				activeChar.sendMessage("=== Test Lock Completed ===");
			}, 200);
		}
		catch (Exception e)
		{
			activeChar.sendMessage("❌ Error testing lock: " + e.getMessage());
		}
	}

	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
}
