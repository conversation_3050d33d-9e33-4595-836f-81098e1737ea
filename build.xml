<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<project name="L2J_Mobius_Classic_3.0_TheKamael" default="cleanup" basedir=".">
	<description>
		This file is part of the L2J Mobius project.
		
		This program is free software: you can redistribute it and/or modify
		it under the terms of the GNU General Public License as published by
		the Free Software Foundation, either version 3 of the License, or
		(at your option) any later version.
		
		This program is distributed in the hope that it will be useful,
		but WITHOUT ANY WARRANTY; without even the implied warranty of
		MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
		General Public License for more details.
		
		You should have received a copy of the GNU General Public License
		along with this program. If not, see [http://www.gnu.org/licenses/].
	</description>

	<property name="build" location="../build" />
	<property name="build.bin" location="${build}/bin" />
	<property name="build.dist" location="${build}/dist" />
	<property name="build.dist.libs" location="${build.dist}/libs" />
	<property name="build.dist.dbinstaller" location="${build.dist}/db_installer" />
	<property name="datapack" location="dist" />
	<property name="libs" location="${datapack}/libs" />
	<property name="src" location="java" />

	<path id="classpath">
		<fileset dir="${libs}">
			<include name="*.jar" />
			<exclude name="**/*-sources.jar" />
		</fileset>
	</path>

	<pathconvert property="manifest.libs" pathsep=" ">
		<path refid="classpath" />
		<mapper>
			<chainedmapper>
				<flattenmapper />
				<globmapper from="*.jar" to="../libs/*.jar" />
			</chainedmapper>
		</mapper>
	</pathconvert>

	<target name="checkRequirements" description="Check Requirements.">
		<fail message="Ant 1.8.2 is required. But your version is ${ant.version} and if you are using Eclipse probably is outdated.">
			<condition>
				<not>
					<antversion atleast="1.8.2" />
				</not>
			</condition>
		</fail>
		<available classname="java.util.stream.Stream" property="JDK21.present" />
		<fail unless="JDK21.present" message="Java 21 is required. But your version is Java ${ant.java.version} and probably JDK is not installed." />
	</target>

	<target name="init" depends="checkRequirements" description="Create the output directories.">
		<delete dir="${build.bin}" quiet="true" />
		<mkdir dir="${build.bin}" />
	</target>

	<target name="compile" depends="init" description="Compile the source.">
		<javac srcdir="${src}" classpathref="classpath" destdir="${build.bin}" compiler="modern" debug="true" debuglevel="lines,vars,source" includeantruntime="false" source="21" target="21" encoding="UTF-8" />
	</target>

	<target name="jar" depends="compile" description="Create the jar files.">
		<tstamp>
			<format property="time.stamp" pattern="yyyy-MM-dd HH:mm:ss z" />
		</tstamp>

		<jar destfile="${build.dist.libs}/LoginServer.jar" level="9">
			<fileset dir="${build.bin}">
				<exclude name="**/dbinstaller/**" />
				<exclude name="**/gameserver/**" />
			</fileset>
			<manifest>
				<attribute name="Build-By" value="${user.name}" />
				<attribute name="Build-Date" value="${time.stamp}" />
				<attribute name="Implementation-URL" value="http://www.l2jmobius.org/" />
				<attribute name="Class-Path" value="${manifest.libs}" />
				<attribute name="Main-Class" value="org.l2jmobius.loginserver.LoginServer" />
			</manifest>
		</jar>
		<jar destfile="${build.dist.libs}/GameServer.jar" level="9">
			<fileset dir="${build.bin}">
				<exclude name="**/dbinstaller/**" />
				<exclude name="**/loginserver/**" />
				<exclude name="**/accountmanager/**" />
				<exclude name="**/gsregistering/**" />
			</fileset>
			<manifest>
				<attribute name="Build-By" value="${user.name}" />
				<attribute name="Build-Date" value="${time.stamp}" />
				<attribute name="Implementation-URL" value="http://www.l2jmobius.org/" />
				<attribute name="Class-Path" value="${manifest.libs}" />
				<attribute name="Main-Class" value="org.l2jmobius.gameserver.GameServer" />
			</manifest>
		</jar>
		<jar destfile="${build.dist.dbinstaller}/Database_Installer_GS.jar" level="9">
			<fileset dir="${build.bin}">
				<include name="**/dbinstaller/**" />
				<include name="**/commons/ui/**" />
				<include name="**/commons/util/**" />
				<include name="**/SQLFilter**" />
				<exclude name="**/LauncherLS*" />
			</fileset>
			<manifest>
				<attribute name="Build-By" value="${user.name}" />
				<attribute name="Build-Date" value="${time.stamp}" />
				<attribute name="Class-Path" value="${manifest.libs}" />
				<attribute name="Implementation-URL" value="http://www.l2jmobius.org/" />
				<attribute name="Main-Class" value="org.l2jmobius.tools.dbinstaller.LauncherGS" />
			</manifest>
		</jar>
		<jar destfile="${build.dist.dbinstaller}/Database_Installer_LS.jar" level="9">
			<fileset dir="${build.bin}">
				<include name="**/dbinstaller/**" />
				<include name="**/commons/ui/**" />
				<include name="**/commons/util/**" />
				<include name="**/SQLFilter**" />
				<exclude name="**/LauncherGS*" />
			</fileset>
			<manifest>
				<attribute name="Build-By" value="${user.name}" />
				<attribute name="Build-Date" value="${time.stamp}" />
				<attribute name="Class-Path" value="${manifest.libs}" />
				<attribute name="Implementation-URL" value="http://www.l2jmobius.org/" />
				<attribute name="Main-Class" value="org.l2jmobius.tools.dbinstaller.LauncherLS" />
			</manifest>
		</jar>
	</target>

	<target name="adding-core" depends="jar" description="Adding the compiled jars to the Zip file.">
		<zip destfile="${build}/L2J_Mobius_Classic_3.0_TheKamael.zip" basedir="${build.dist}" level="9" />
	</target>

	<target name="adding-datapack" depends="adding-core" description="Updating the Zip file with datapack content.">
		<zip destfile="${build}/L2J_Mobius_Classic_3.0_TheKamael.zip" basedir="${datapack}" excludes="**/*-sources.jar" update="true" level="9" />
	</target>

	<target name="adding-readme" depends="adding-datapack" description="Adding readme.txt to the Zip file.">
		<zip destfile="${build}/L2J_Mobius_Classic_3.0_TheKamael.zip" basedir="." includes="readme.txt" update="true" level="9" />
	</target>

	<target name="cleanup" depends="adding-readme" description="Cleaning the build folder.">
		<delete dir="${build.dist}" />
	</target>
</project>