/*
 * Copyright (c) 2013 L2jMobius
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
 * IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
package org.l2jmobius.gameserver.data.sql;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.gameserver.model.actor.Player;

/**
 * <AUTHOR> GolbergSoft
 */
public class CostumeTable
{
	private static final Logger LOGGER = Logger.getLogger(CostumeTable.class.getName());

	private int _playerId;
	private int _id;
	private long _amount;
	private boolean _locked;
	private boolean _isNew;
	
	public static CostumeTable of(int costumeId, Player player)
	{
		final CostumeTable data = new CostumeTable();
		data._playerId = player.getObjectId();
		data._id = costumeId;
		data._isNew = true;
		return data;
	}
	
	public void increaseAmount()
	{
		_amount++;
		update();
	}
	
	public void setLockedAndUpdate(boolean lock)
	{
		_locked = lock;
		update();
	}
	
	public void setLocked(boolean lock)
	{
		_locked = lock;
	}
	
	public boolean isLocked()
	{
		return _locked;
	}
	
	public void reduceCount(long amount)
	{
		_amount -= amount;
		update();
	}
	
	public int getPlayerId()
	{
		return _playerId;
	}
	
	public void setPlayerId(int playerId)
	{
		_playerId = playerId;
	}
	
	public int getId()
	{
		return _id;
	}
	
	public void setId(int id)
	{
		_id = id;
	}
	
	public long getAmount()
	{
		return _amount;
	}
	
	public void setAmount(long amount)
	{
		_amount = amount;
	}
	
	public void setNew(boolean isNew)
	{
		_isNew = isNew;
	}
	
	public boolean checkIsNewAndChange()
	{
		final boolean ret = _isNew;
		_isNew = false;
		return ret;
	}
	
	private void update()
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement("UPDATE character_costumes SET amount = ?, locked = ? WHERE player_id = ? AND id = ?"))
		{
			ps.setLong(1, _amount);
			ps.setBoolean(2, _locked);
			ps.setInt(3, _playerId);
			ps.setInt(4, _id);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Error updating costume: " + e.getMessage());
		}
	}

	public void save()
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement("INSERT INTO character_costumes (player_id, id, amount, locked) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE amount = VALUES(amount), locked = VALUES(locked)"))
		{
			ps.setInt(1, _playerId);
			ps.setInt(2, _id);
			ps.setLong(3, _amount);
			ps.setBoolean(4, _locked);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Error saving costume: " + e.getMessage());
		}
	}
	
	public void load()
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement("SELECT amount, locked FROM character_costumes WHERE player_id = ? AND id = ?"))
		{
			ps.setInt(1, _playerId);
			ps.setInt(2, _id);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					_amount = rs.getLong("amount");
					_locked = rs.getBoolean("locked");
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public static List<CostumeTable> getCostumesForPlayer(Player player)
	{
		final List<CostumeTable> costumes = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement("SELECT id, amount, locked FROM character_costumes WHERE player_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					final CostumeTable data = new CostumeTable();
					data._playerId = player.getObjectId();
					data._id = rs.getInt("id");
					data._amount = rs.getLong("amount");
					data._locked = rs.getBoolean("locked");
					data._isNew = false; // Assume that costumes loaded from DB are not new.
					costumes.add(data);
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return costumes;
	}
}
