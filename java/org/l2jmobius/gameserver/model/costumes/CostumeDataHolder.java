/*
 * Copyright (c) 2013 L2jMobius
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
 * IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
package org.l2jmobius.gameserver.model.costumes;

import org.l2jmobius.gameserver.model.StatSet;

/**
 * <AUTHOR>
 */
public class CostumeDataHolder
{
	private final int _id;
	private final StatSet _attributes;
	
	/**
	 * Constructs a new CostumeDataHolder.
	 * @param statSet the StatSet containing attributes of the costume
	 */
	public CostumeDataHolder(StatSet statSet)
	{
		_id = statSet.getInt("id");
		_attributes = statSet;
	}
	
	/**
	 * Gets the ID of the costume.
	 * @return the ID of the costume
	 */
	public int getId()
	{
		return _id;
	}
	
	/**
	 * Gets the value of a specific attribute for the costume.
	 * @param attributeName the name of the attribute
	 * @return the value of the attribute, or null if the attribute does not exist
	 */
	public String getAttribute(String attributeName)
	{
		return _attributes.getString(attributeName);
	}
}