package org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.l2jmobius.Config;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.data.xml.DynamicExpRateData;
import org.l2jmobius.gameserver.data.xml.ItemData;
import org.l2jmobius.gameserver.data.xml.NpcData;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.templates.NpcTemplate;
import org.l2jmobius.gameserver.model.holders.DropGroupHolder;
import org.l2jmobius.gameserver.model.holders.DropHolder;
import org.l2jmobius.gameserver.model.item.ItemTemplate;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.model.itemcontainer.Inventory;
import org.l2jmobius.gameserver.model.skill.Skill;
import org.l2jmobius.gameserver.model.stats.MoveType;
import org.l2jmobius.gameserver.model.stats.Stat;
import org.l2jmobius.gameserver.network.serverpackets.NpcHtmlMessage;
import org.l2jmobius.gameserver.util.Util;

/**
 * <AUTHOR>
 */
public class DropInfoFunctions
{
	public static String getMinMaxDropCounts(NpcTemplate monster, int itemId, boolean drop)
	{
		long min = getDropMinMaxCounts(monster, itemId, drop, true);
		long max = getDropMinMaxCounts(monster, itemId, drop, false);
		String formattedCounts = "[" + min + "..." + max + ']';
		return formattedCounts;
	}
	
	private static long getDropMinMaxCounts(NpcTemplate template, int itemId, boolean isSpoil, boolean min)
	{
		long result = 0;
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		for (DropInfoHolder drop : drops)
		{
			if (drop.getNpcId() == template.getId())
			{
				if (drop.isSweep() && isSpoil)
				{
					return min ? drop.getMin() : drop.getMax();
				}
				else if (!drop.isSweep() && !isSpoil)
				{
					return min ? drop.getMin() : drop.getMax();
				}
			}
		}
		return result;
	}
	
	public static String getDropChance(NpcTemplate template, int itemId, boolean isSpoil, Player player)
	{
		double chance = 0.0;
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		if (drops == null)
		{
			return Util.formatDouble(chance, "#.####");
		}
		double rateChance = 1.0; // Khởi tạo tỷ lệ cơ bản
		for (DropInfoHolder drop : drops)
		{
			if (drop.getNpcId() == template.getId())
			{
				// Xử lý spoil trước
				if (drop.isSweep() && isSpoil)
				{
					// Điều chỉnh tỷ lệ spoil dựa trên cấu hình và cấp độ người chơi
					rateChance = Config.RATE_SPOIL_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicSpoilChance(player.getLevel());
					// Áp dụng bonus spoil rate từ người chơi
					rateChance *= player.getStat().getMul(Stat.BONUS_SPOIL_RATE, 1);
					// Kiểm tra Premium Status của người chơi
					if (Config.PREMIUM_SYSTEM_ENABLED && player.hasPremiumStatus())
					{
						rateChance *= Config.PREMIUM_RATE_SPOIL_CHANCE;
					}
					return Util.formatDouble(drop.getChance() * rateChance, "#.####");
				}
				// Xử lý drop
				else if (!drop.isSweep() && !isSpoil)
				{
					// Điều chỉnh tỷ lệ drop dựa trên cấu hình của từng item
					if (Config.RATE_DROP_CHANCE_BY_ID.get(drop.getItemId()) != null)
					{
						rateChance *= Config.RATE_DROP_CHANCE_BY_ID.get(drop.getItemId()) * DynamicExpRateData.getInstance().getDynamicAdenaChance(player.getLevel());
					}
					// Kiểm tra nếu là item dạng herb
					else if (ItemData.getInstance().getTemplate(drop.getItemId()).hasExImmediateEffect())
					{
						rateChance *= Config.RATE_HERB_DROP_CHANCE_MULTIPLIER;
					}
					// Nếu là Raid Boss
					else if (template.getType().equalsIgnoreCase("RaidBoss") || template.getType().equalsIgnoreCase("GrandBoss"))
					{
						rateChance *= Config.RATE_RAID_DROP_CHANCE_MULTIPLIER;
					}
					else
					{
						// Điều chỉnh tỷ lệ drop dựa trên cấp độ người chơi
						rateChance *= Config.RATE_DEATH_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicDropChance(player.getLevel());
					}
					// Áp dụng bonus drop rate từ người chơi
					rateChance *= player.getStat().getMul(Stat.BONUS_DROP_RATE, 1);
					// Kiểm tra Premium Status của người chơi
					if (Config.PREMIUM_SYSTEM_ENABLED && player.hasPremiumStatus())
					{
						if (Config.PREMIUM_RATE_DROP_CHANCE_BY_ID.get(drop.getItemId()) != null)
						{
							rateChance *= Config.PREMIUM_RATE_DROP_CHANCE_BY_ID.get(drop.getItemId());
						}
						else
						{
							rateChance *= Config.PREMIUM_RATE_DROP_CHANCE;
						}
					}
					// Kiểm tra các trường hợp đặc biệt về tỷ lệ rơi của quái Forgotten Island
					boolean isMobForgottenIsland = false;
					switch (template.getId())
					{
						case 21746:
						case 21737:
						case 21748:
						case 21747:
						case 21753:
						case 21750:
						case 21749:
						case 21739:
						case 21738:
						case 21752:
						case 21741:
						case 21757:
						case 21740:
						case 21733:
						case 21734:
						case 21735:
						case 21736:
						case 21742:
						case 21743:
						case 21744:
						case 21745:
						case 21756:
						case 21759:
						case 21760:
							isMobForgottenIsland = true;
							break;
					}
					if (isMobForgottenIsland && drop.getItemId() == Inventory.ADENA_ID)
					{
						rateChance *= 0.5;
					}
					if (itemId == 955) // Scroll: Enchant Weapon (D-grade)
					{
						rateChance *= 0.3;
					}
					return Util.formatDouble(drop.getChance() * rateChance, "#.####");
				}
			}
		}
		return Util.formatDouble(chance, "#.####");
	}
	
	public static int getDropsCount(NpcTemplate template, boolean spoil)
	{
		int dropCounts = 0;
		for (ArrayList<DropInfoHolder> drop : DropInfoHandler.getInstance().getInfo().values())
		{
			for (int i = 0; i < drop.size(); i++)
			{
				DropInfoHolder itemDrop = drop.get(i);
				if (itemDrop.getNpcId() == template.getId())
				{
					if (itemDrop.isSweep() && spoil)
					{
						dropCounts++;
					}
					else if (!itemDrop.isSweep() && !spoil)
					{
						dropCounts++;
					}
				}
			}
		}
		return dropCounts;
	}
	
	public static void showNpcDropList(Player activeChar, String dropType, int npcId, int page)
	{
		// Lấy template của NPC
		NpcTemplate npcData = NpcData.getInstance().getTemplate(npcId);
		if (npcData == null)
		{
			activeChar.sendMessage("Unknown NPC template ID " + npcId);
			return;
		}
		// Tạo HTML trả lời
		final StringBuilder replyMSG = new StringBuilder();
		replyMSG.append("<html><title>Show drop/spoil list page ").append(page).append("</title><body><br><center><font color=\"LEVEL\">").append(npcData.getName()).append(" (").append(npcId).append(")</font><br>");
		// Khai báo biến itemsPerPage
		int itemsPerPage = 10; // Số lượng item mỗi trang
		// Tạo danh sách drop tổng hợp
		List<DropHolder> combinedDropList = new ArrayList<>();
		if (dropType.equals("DROP"))
		{
			// Lấy danh sách drop cơ bản
			List<DropHolder> dropList = npcData.getDropList();
			if (dropList != null)
			{
				combinedDropList.addAll(dropList);
			}
			// Lấy danh sách nhóm drop
			List<DropGroupHolder> dropGroups = npcData.getDropGroups();
			if (dropGroups != null)
			{
				for (DropGroupHolder group : dropGroups)
				{
					double groupChance = group.getChance(); // Xác suất của nhóm
					for (DropHolder drop : group.getDropList())
					{
						// Skip itemid 91754 and 91755 (L-Coin chests)
						if (drop.getItemId() == 91754 || drop.getItemId() == 91755)
						{
							continue;
						}

						// Tính toán xác suất thực tế của item dựa trên xác suất của nhóm
						double itemChance = drop.getChance() * (groupChance / 100.0);
						if (drop.getItemId() == 955)
						{
							itemChance = 30.0; // Đặt tỉ lệ chance thành 30% cho item ID 955
						}
						DropHolder adjustedDrop = new DropHolder(drop.getDropType(), drop.getItemId(), drop.getMin(), drop.getMax(), itemChance);
						combinedDropList.add(adjustedDrop);
					}
				}
			}
		}
		else if (dropType.equals("SPOIL"))
		{
			List<DropHolder> spoilList = npcData.getSpoilList();
			if (spoilList != null)
			{
				combinedDropList.addAll(spoilList);
			}
		}
		else
		{
			activeChar.sendMessage("Invalid drop type: " + dropType);
			return;
		}
		// Kiểm tra danh sách drop/spoil tổng hợp
		if (combinedDropList.isEmpty())
		{
			replyMSG.append("<br><center>No ").append(dropType.equals("DROP") ? "drops" : "spoils").append(" available for this NPC.</center><br>");
		}
		else
		{
			// Tính toán phân trang
			int totalItems = combinedDropList.size();
			int totalPages = (int) Math.ceil((double) totalItems / itemsPerPage);
			if (page > totalPages)
			{
				page = totalPages;
			}
			int startIndex = (page - 1) * itemsPerPage;
			int endIndex = Math.min(startIndex + itemsPerPage, totalItems);
			// Hiển thị từng item
			for (int i = startIndex; i < endIndex; i++)
			{
				DropHolder dropHolder = combinedDropList.get(i);
				// Bỏ qua các loại HERB (nếu cần thiết)
				if (DropInfoHandler.HERBS.contains(dropHolder.getItemId()))
				{
					continue;
				}
				// Lấy thông tin item
				ItemTemplate item = ItemData.getInstance().getTemplate(dropHolder.getItemId());
				if (item != null)
				{
					double chance = dropHolder.getChance();
					if (item.getId() == 955)
					{
						chance *= 0.3;
					}
					// Hiển thị thông tin item, bao gồm tỷ lệ rơi và số lượng min-max
					replyMSG.append("<br1><center><img src=\"l2ui.squaregray\" width=\"280\" height=\"2\"></center>").append("<center><table border=0 cellpadding=0 cellspacing=0 width=\"280\" height=\"40\" bgcolor=\"2E2E2E\"><tr>").append("<td FIXWIDTH=32><table><tr><td width=32 align=right valign=top><table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=\"").append(item.getIcon()).append("\"><tr><td width=32 height=32 align=center valign=top>").append("<button value=\" \" action=\"bypass -h _bbssearchdropMonstersByItem_").append(item.getId()).append("_1\" width=32 height=32 back=\"L2UI_CT1.ItemWindow_DF_Frame_Down\" fore=\"L2UI_CT1.ItemWindow_DF_Frame\"></button>").append("</td></tr></table></td></tr></table></td>").append("<td FIXWIDTH=200 align=center valign=top><font color=\"F4FA58\" name=\"hs9\">").append(getNameLong(item.getName())).append("</font><br1>").append("<font color=\"F4FA58\">Amount: ").append(dropHolder.getMin()).append(" - ").append(dropHolder.getMax()).append("</font><br1><font color=\"5882FA\">Chance: ").append(Util.formatDouble(chance, "#.#####")).append("%</font></td></tr></table></center>");
				}
			}
			// Logic phân trang
			replyMSG.append("<br><table width=280 bgcolor=666666 border=0><tr>");
			if (page > 1)
			{
				replyMSG.append("<td width=120><button value=\" \" action=\"bypass -h _bbssearchNpcDropList_").append(dropType).append("_").append(npcId).append("_").append(page - 1).append("\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\"></button></td>");
			}
			replyMSG.append("<td width=100 align=center>Page ").append(page).append(" of ").append(totalPages).append("</td>");
			if (page < totalPages)
			{
				replyMSG.append("<td width=120 align=right><button value=\" \" action=\"bypass -h _bbssearchNpcDropList_").append(dropType).append("_").append(npcId).append("_").append(page + 1).append("\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\"></button></td>");
			}
			replyMSG.append("</tr></table>");
		}
		replyMSG.append("</body></html>");
		// Gửi HTML trả lời cho người chơi
		NpcHtmlMessage adminReply = new NpcHtmlMessage();
		adminReply.setHtml(replyMSG.toString());
		activeChar.sendPacket(adminReply);
	}
	
	public static void showNpcSkillList(Player activeChar, int npcId, int page)
	{
		NpcTemplate npcData = NpcData.getInstance().getTemplate(npcId);
		if (npcData == null)
		{
			activeChar.sendMessage("Template id unknown: " + npcId);
			return;
		}
		Map<Integer, Skill> skills = new HashMap<>(npcData.getSkills());
		int _skillsize = skills.size();
		int MaxSkillsPerPage = 20;
		int MaxPages = _skillsize / MaxSkillsPerPage;
		if (_skillsize > (MaxSkillsPerPage * MaxPages))
		{
			MaxPages++;
		}
		if (page > MaxPages)
		{
			page = MaxPages;
		}
		int SkillsStart = MaxSkillsPerPage * page;
		int SkillsEnd = _skillsize;
		if ((SkillsEnd - SkillsStart) > MaxSkillsPerPage)
		{
			SkillsEnd = SkillsStart + MaxSkillsPerPage;
		}
		StringBuffer replyMSG = new StringBuffer("<html><title>NPC Skill List</title><body><center><font color=\"LEVEL\">");
		replyMSG.append(npcData.getName());
		replyMSG.append(" (");
		replyMSG.append(npcData.getId());
		replyMSG.append("): ");
		replyMSG.append(_skillsize);
		replyMSG.append(" skills</font></center><table width=300 bgcolor=666666><tr>");
		for (int x = 0; x < MaxPages; x++)
		{
			int pagenr = x + 1;
			if (page == x)
			{
				replyMSG.append("<td>Page ");
				replyMSG.append(pagenr);
				replyMSG.append("</td>");
			}
			else
			{
				replyMSG.append("<td><a action=\"bypass -h _bbssearchShowSkills_");
				replyMSG.append(npcData.getId());
				replyMSG.append("_");
				replyMSG.append(x);
				replyMSG.append("\"> Page ");
				replyMSG.append(pagenr);
				replyMSG.append(" </a></td>");
			}
		}
		replyMSG.append("</tr></table><br><table width=\"100%\" border=0><tr><td>Skill name [skill id-skill lvl]</td></tr>");
		Iterator<Skill> skillite = skills.values().iterator();
		for (int i = 0; i < SkillsStart; i++)
		{
			if (skillite.hasNext())
			{
				skillite.next();
			}
		}
		int cnt = SkillsStart;
		Skill sk;
		while (skillite.hasNext())
		{
			cnt++;
			if (cnt > SkillsEnd)
			{
				break;
			}
			sk = skillite.next();
			replyMSG.append("<tr><td width=240>");
			replyMSG.append(sk.getName());
			replyMSG.append(" [");
			replyMSG.append(sk.getId());
			replyMSG.append("-");
			replyMSG.append(sk.getLevel());
			replyMSG.append("]</td></tr>");
		}
		NpcHtmlMessage adminReply = new NpcHtmlMessage();
		adminReply.setHtml(replyMSG.toString());
		activeChar.sendPacket(adminReply);
	}
	
	public static void showStats(Player activeChar, int npcId)
	{
		final NpcHtmlMessage html = new NpcHtmlMessage();
		String html1 = HtmCache.getInstance().getHtm(activeChar, "data/html/CommunityBoard/Custom/DropCalculator/bbs_mobStats.htm");
		NpcTemplate target = NpcData.getInstance().getTemplate(npcId);
		if (target != null)
		{
			// Combat Stats
			html1 = html1.replace("%patk%", Util.formatDouble(target.getBasePAtk(), "#.##"));
			html1 = html1.replace("%matk%", Util.formatDouble(target.getBaseMAtk(), "#.##"));
			html1 = html1.replace("%pdef%", Util.formatDouble(target.getBasePDef(), "#.##"));
			html1 = html1.replace("%mdef%", Util.formatDouble(target.getBaseMDef(), "#.##"));
			html1 = html1.replace("%accu%", "N/A");
			html1 = html1.replace("%evas%", "N/A");
			html1 = html1.replace("%crit%", Util.formatDouble(target.getBaseCritRate(), "#.##"));
			html1 = html1.replace("%rspd%", Util.formatDouble(target.getBaseMoveSpeed(MoveType.RUNNING), "#.##"));
			html1 = html1.replace("%aspd%", Util.formatDouble(target.getBasePAtkSpd(), "#.##"));
			html1 = html1.replace("%cspd%", Util.formatDouble(target.getBaseMAtkSpd(), "#.##"));
			// Basic Stats
			html1 = html1.replace("%str%", Util.formatDouble(target.getBaseSTR(), "#.##"));
			html1 = html1.replace("%dex%", Util.formatDouble(target.getBaseDEX(), "#.##"));
			html1 = html1.replace("%con%", Util.formatDouble(target.getBaseCON(), "#.##"));
			html1 = html1.replace("%int%", Util.formatDouble(target.getBaseINT(), "#.##"));
			html1 = html1.replace("%wit%", Util.formatDouble(target.getBaseWIT(), "#.##"));
			html1 = html1.replace("%men%", Util.formatDouble(target.getBaseMEN(), "#.##"));
			// Elements Stats
			html1 = html1.replace("%ele_atk%", "N/A");
			html1 = html1.replace("%ele_atk_value%", "N/A");
			html1 = html1.replace("%ele_dfire%", Util.formatDouble(target.getBaseFireRes(), "#.##"));
			html1 = html1.replace("%ele_dwater%", Util.formatDouble(target.getBaseWaterRes(), "#.##"));
			html1 = html1.replace("%ele_dwind%", Util.formatDouble(target.getBaseWindRes(), "#.##"));
			html1 = html1.replace("%ele_dearth%", Util.formatDouble(target.getBaseEarthRes(), "#.##"));
			html1 = html1.replace("%ele_dholy%", Util.formatDouble(target.getBaseHolyRes(), "#.##"));
			html1 = html1.replace("%ele_ddark%", Util.formatDouble(target.getBaseDarkRes(), "#.##"));
		}
		html.setHtml(html1.toString());
		activeChar.sendPacket(html);
	}
	
	public static int getDroplistsCountByItemId(int itemId, boolean isSpoil)
	{
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		if (drops == null)
		{
			return 0;
		}
		int dropCounts = 0;
		for (DropInfoHolder drop : drops)
		{
			if (drop.isSweep() && isSpoil)
			{
				dropCounts++;
			}
			else if (!drop.isSweep() && !isSpoil)
			{
				dropCounts++;
			}
		}
		return dropCounts;
	}
	
	public static String getName(String name)
	{
		if (name.length() > 20)
		{
			return name.substring(0, 19) + "...";
		}
		return name;
	}
	
	public static String getNameLong(String name)
	{
		if (name.length() > 36)
		{
			return name.substring(0, 35) + "...";
		}
		return name;
	}
	
	public static Npc getAliveNpc(int npcId)
	{
		List<Npc> instances = World.getInstance().getAllByNpcId(npcId);
		return instances.isEmpty() ? null : instances.get(0);
	}
	
	public static List<ItemTemplate> getItemsByNameContainingString(String itemName)
	{
		List<ItemTemplate> itemsByName = new ArrayList<>();
		for (ItemTemplate item : ItemData.getInstance().getAllItems())
		{
			if (item != null && item.getName() != null)
			{
				if (item.getName().toLowerCase().contains(itemName.toLowerCase()))
				{
					itemsByName.add(item);
				}
			}
		}
		return itemsByName;
	}
	
	public static List<ItemTemplate> sortItemTemplates(List<ItemTemplate> items, int sort)
	{
		Collections.sort(items, new ItemTemplateComparator(sort));
		return items;
	}
	
	public static List<NpcTemplate> getNpcsContainingString(String monsterName)
	{
		List<NpcTemplate> npcTemplates = new ArrayList<>();
		for (NpcTemplate npcTemplate : NpcData.getInstance().getAllNpcs())
		{
			if (npcTemplate.getName().toLowerCase().contains(monsterName.toLowerCase()))
			{
				npcTemplates.add(npcTemplate);
			}
		}
		return npcTemplates;
	}
	
	public static List<Item> sortItems(List<Item> itemsByName, int sort)
	{
		Collections.sort(itemsByName, new ItemComparator(sort));
		return itemsByName;
	}
	
	private static class ItemComparator implements Comparator<Item>, Serializable
	{
		private static final long	serialVersionUID	= -6389059445439769861L;
		private final int			sort;
		
		protected ItemComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(Item o1, Item o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1: // By drops count
					return Integer.compare(getDroplistsCountByItemId(o2.getId(), false), getDroplistsCountByItemId(o1.getId(), false));
				case 2:// By spoil count
					return Integer.compare(getDroplistsCountByItemId(o2.getId(), true), getDroplistsCountByItemId(o1.getId(), true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	public static List<NpcTemplate> sortMonsters(List<NpcTemplate> npcTemplates, int sort)
	{
		Collections.sort(npcTemplates, new MonsterComparator(sort));
		return npcTemplates;
	}
	
	private static class MonsterComparator implements Comparator<NpcTemplate>, Serializable
	{
		private static final long	serialVersionUID	= 2116090903265145828L;
		private final int			sort;
		
		protected MonsterComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(NpcTemplate o1, NpcTemplate o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1:// By drops count
					return Integer.compare(getDropsCount(o2, false), getDropsCount(o1, false));
				case 2:// By spoil count
					return Integer.compare(getDropsCount(o2, true), getDropsCount(o1, true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	public static List<DropInfoHolder> sortMonsters2(List<DropInfoHolder> templates, int sort)
	{
		if (templates == null)
		{
			templates = new ArrayList<>();
		}
		Collections.sort(templates, new MonstersComparator(sort));
		return templates;
	}
	
	private static class MonstersComparator implements Comparator<DropInfoHolder>, Serializable
	{
		private static final long	serialVersionUID	= -3803552841261367731L;
		private final int			sort;
		
		protected MonstersComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(DropInfoHolder o1, DropInfoHolder o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1:// By level
					return Integer.compare(o2.getLevel(), o1.getLevel());
				case 2:// By chance
					return Double.compare(o2.getChance(), o1.getChance());
				case 3:// By type
					return Boolean.compare(o2.isSweep(), o1.isSweep());
				case 4:// By drop count
					return Long.compare(o2.getMin() + o2.getMax(), o1.getMin() + o1.getMax());
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	private static class ItemTemplateComparator implements Comparator<ItemTemplate>, Serializable
	{
		private static final long	serialVersionUID	= -6389059445439769861L;
		private final int			sort;
		
		protected ItemTemplateComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(ItemTemplate o1, ItemTemplate o2)
		{
			switch (sort)
			{
				case 0:
					return o1.getName().compareTo(o2.getName());
				case 1:
					return Integer.compare(DropInfoFunctions.getDroplistsCountByItemId(o2.getId(), false), DropInfoFunctions.getDroplistsCountByItemId(o1.getId(), false));
				case 2:
					return Integer.compare(DropInfoFunctions.getDroplistsCountByItemId(o2.getId(), true), DropInfoFunctions.getDroplistsCountByItemId(o1.getId(), true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
}
