/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.communitybbs.Manager;

import java.util.ArrayList;
import java.util.List;

import org.l2jmobius.gameserver.data.xml.SkillData;
import org.l2jmobius.gameserver.model.skill.Skill;

/**
 * <AUTHOR>
 */
public class PlayerScheme
{
	public final int				schemeId;
	public String					schemeName;
	public int						iconId;
	public final List<SchemeBuff>	schemeBuffs;
	public int						totalCostLcoin;	// Tổng chi phí Lcoin của scheme
	
	public PlayerScheme(int schemeId, String schemeName, int iconId, int totalCostLcoin)
	{
		this.schemeId = schemeId;
		this.schemeName = schemeName;
		this.iconId = iconId;
		this.totalCostLcoin = totalCostLcoin;
		schemeBuffs = new ArrayList<>();
	}
	
	// Getter cho schemeId
	public int getId()
	{
		return schemeId;
	}
	
	// Getter và setter cho schemeName
	public String getSchemeName()
	{
		return schemeName;
	}
	
	public void setSchemeName(String schemeName)
	{
		this.schemeName = schemeName;
	}
	
	// Getter và setter cho totalCostLcoin
	public int getTotalCostLcoin()
	{
		return totalCostLcoin;
	}
	
	public void setTotalCostLcoin(int totalCostLcoin)
	{
		this.totalCostLcoin = totalCostLcoin;
	}
	
	// Getter cho iconId (nếu cần sử dụng)
	public int getIconId()
	{
		return iconId;
	}
	
	public List<SchemeBuff> getSchemeBuffs()
	{
		return schemeBuffs;
	}
	
	public List<Skill> getBuffs()
	{
		List<Skill> buffs = new ArrayList<>();
		for (SchemeBuff buff : schemeBuffs)
		{
			Skill skill = SkillData.getInstance().getSkill(buff.skillId, buff.skillLevel);
			if (skill != null)
			{
				buffs.add(skill);
			}
		}
		return buffs;
	}
}
